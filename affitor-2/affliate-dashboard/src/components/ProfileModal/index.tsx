"use client";

import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { useTheme } from "next-themes";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Settings,
  Users,
  Rocket,
  Moon,
  Sun,
  LogOut,
  ChevronRight,
} from "lucide-react";
import { actions as authActions } from "@/features/auth/auth.slice";
import { actions as userActions } from "@/features/user/user.slice";
import {
  selectUserData,
  selectUserLoading,
  selectUserError,
} from "@/features/selectors";

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode; // The Profile button that triggers the modal
}

export const ProfileModal: React.FC<ProfileModalProps> = ({
  isOpen,
  onClose,
  children,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const userData = useSelector(selectUserData);
  const loading = useSelector(selectUserLoading);
  const error = useSelector(selectUserError);

  // Use the same data binding approach as the Profile container
  React.useEffect(() => {
    if (isOpen && !userData) {
      dispatch(userActions.fetchUserMe());
    }
  }, [dispatch, isOpen, userData]);

  // Use the same data calculation approach as the Profile container
  const subscription_tier = userData?.user_tracking_request?.subscription_tier;
  const transaction = userData?.user_tracking_request?.transaction;
  const request_limit = userData?.user_tracking_request?.request_limit || 0;

  // Check if subscription is cancelled but still active (grace period)
  const isCancelled =
    transaction?.cancellation_date !== null &&
    transaction?.cancellation_date !== undefined;
  const willAutoRenew = transaction?.auto_renew === true;

  // Check if user has unlimited requests (request_limit > 10000)
  const hasUnlimitedRequests = request_limit > 10000;

  const usageData = userData?.user_tracking_request
    ? {
        usageCount: userData.user_tracking_request.request_count,
        maxRequests: userData.user_tracking_request.request_limit,
        last_request_date: new Date(
          userData.user_tracking_request.last_request_date
        ),
        // Calculate days left using current_period_end
        daysLeft: userData.user_tracking_request.current_period_end
          ? Math.max(
              0,
              Math.floor(
                (new Date(
                  userData.user_tracking_request.current_period_end
                ).getTime() -
                  new Date().getTime()) /
                  (1000 * 60 * 60 * 24)
              )
            )
          : 30,
        tier: subscription_tier,
        isCancelled: isCancelled,
        willAutoRenew: willAutoRenew,
        cancellationDate: transaction?.cancellation_date
          ? new Date(transaction.cancellation_date)
          : null,
        cancellationReason: transaction?.cancellation_reason,
        currentPeriodEnd: transaction?.current_period_end
          ? new Date(transaction.current_period_end)
          : null,
        hasUnlimitedRequests: hasUnlimitedRequests,
      }
    : {
        usageCount: 0,
        maxRequests: 0,
        daysLeft: 30,
        tier: subscription_tier,
        isCancelled: false,
        willAutoRenew: false,
        cancellationDate: null,
        cancellationReason: null,
        currentPeriodEnd: null,
        hasUnlimitedRequests: false,
      };

  const usagePercentage =
    usageData.maxRequests > 0
      ? (usageData.usageCount / usageData.maxRequests) * 100
      : 0;

  const formatDateYMD = (date?: string | Date): string => {
    if (!date) return "N/A";
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toISOString().split('T')[0]; // Returns yyyy-mm-dd format
  };

  // Determine if user has a paid subscription
  const hasPaidSubscription = !!subscription_tier;

  // Determine if user should see upgrade button (Free or Pro users)
  const shouldShowUpgradeButton = () => {
    if (!subscription_tier) {
      // No subscription tier means Free user
      return true;
    }

    const tierName = subscription_tier.display_name?.toLowerCase() || '';
    // Show upgrade button for Free, Basic, and Pro users, hide for Premium users
    return (
      tierName === 'free' ||
      tierName === 'basic' ||
      tierName === 'pro' ||
      tierName === 'pro plan' ||
      tierName.includes('free') ||
      tierName.includes('basic') ||
      (tierName.includes('pro') && !tierName.includes('premium'))
    );
  };

  const handleSignOut = () => {
    dispatch(authActions.logout());
    dispatch(userActions.clearUserData());
    onClose();
    router.push("/");
  };

  const handleUserSettings = () => {
    onClose();
    router.push("/profile");
  };

  const handleBecomeAffiliate = () => {
    onClose();
    router.push("/affiliate");
  };

  const handleUpgradePlan = () => {
    onClose();
    router.push("/pricing");
  };

  // Close modal when clicking outside or pressing escape
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  if (loading) {
    return (
      <Popover open={isOpen} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          {children}
        </PopoverTrigger>
        <PopoverContent
          className="w-80 p-4"
          align="end"
          side="bottom"
          sideOffset={8}
        >
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading...</span>
          </div>
        </PopoverContent>
      </Popover>
    );
  }

  if (error) {
    return (
      <Popover open={isOpen} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          {children}
        </PopoverTrigger>
        <PopoverContent
          className="w-80 p-4"
          align="end"
          side="bottom"
          sideOffset={8}
        >
          <div className="text-center py-4">
            <p className="text-red-600 dark:text-red-400 text-sm">
              Failed to load profile data
            </p>
            <Button
              onClick={() => dispatch(userActions.fetchUserMe())}
              variant="outline"
              size="sm"
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent
        className="w-80 p-0 gap-0 bg-gray-900 border-gray-700"
        align="end"
        side="bottom"
        sideOffset={8}
      >
        <div className="bg-gray-900 text-white rounded-lg">
          {/* Header with user info */}
          <div className="p-4">
            {/* User profile section */}
            <div className="flex items-center space-x-3 mb-4">
              <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center text-white font-semibold text-lg">
                {userData?.username?.charAt(0)?.toUpperCase() || "S"}
              </div>
              <div>
                <h3 className="font-semibold text-white">
                  {userData?.username || "User"}'s Lovable
                </h3>
                <p className="text-sm text-gray-400">
                  {userData?.email || "<EMAIL>"}
                </p>
              </div>
            </div>

            {/* Credits Used Section */}
            <div className="bg-gray-800 rounded-lg p-4 mb-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-white font-medium">Credits Used</h3>
                <button className="text-gray-400 hover:text-white text-sm">
                  Manage
                </button>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Plan</span>
                  <span className="text-white">
                    {usageData.hasUnlimitedRequests ? (
                      "Unlimited"
                    ) : (
                      `${usageData.usageCount}/${usageData.maxRequests}`
                    )}
                  </span>
                </div>
                <Progress
                  value={usageData.hasUnlimitedRequests ? 15 : usagePercentage}
                  className="h-2 bg-gray-700"
                />
                <p className="text-xs text-gray-400">
                  You have 5 daily credits to use first
                </p>
              </div>
            </div>

            {/* Action Buttons Row */}
            <div className="flex gap-2 mb-4">
              <Button
                onClick={handleUserSettings}
                variant="ghost"
                className="flex-1 bg-gray-800 hover:bg-gray-700 text-white border-gray-700 rounded-lg h-10"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>

          {/* Menu Items */}
          <div className="space-y-1">
            <Button
              onClick={handleBecomeAffiliate}
              variant="ghost"
              className="w-full justify-start gap-3 h-10 text-sm text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg"
            >
              <Users className="w-4 h-4" />
              Become an Affiliate
            </Button>

            {/* Upgrade Plan - Show for Free and Pro users, hide for Premium users */}
            {shouldShowUpgradeButton() && (
              <Button
                onClick={handleUpgradePlan}
                variant="ghost"
                className="w-full justify-start gap-3 h-10 text-sm text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg"
              >
                <Rocket className="w-4 h-4" />
                Upgrade Plan
              </Button>
            )}

            <Button
              onClick={toggleTheme}
              variant="ghost"
              className="w-full justify-start gap-3 h-10 text-sm text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg"
            >
              {theme === "light" ? (
                <Moon className="w-4 h-4" />
              ) : (
                <Sun className="w-4 h-4" />
              )}
              Appearance
              <ChevronRight className="w-4 h-4 ml-auto" />
            </Button>

            <Button
              onClick={handleSignOut}
              variant="ghost"
              className="w-full justify-start gap-3 h-10 text-sm text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg"
            >
              <LogOut className="w-4 h-4" />
              Sign out
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ProfileModal;
